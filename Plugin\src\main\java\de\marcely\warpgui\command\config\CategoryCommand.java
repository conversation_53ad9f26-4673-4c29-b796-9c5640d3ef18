package de.marcely.warpgui.command.config;

import de.marcely.warpgui.WarpGUIPlugin;
import de.marcely.warpgui.category.WarpCategory;
import de.marcely.warpgui.command.Command;
import de.marcely.warpgui.storage.CategoryStorage;
import de.marcely.warpgui.Message;
import org.bukkit.command.CommandSender;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class CategoryCommand extends Command.Executor {
    
    public CategoryCommand(WarpGUIPlugin plugin) {
        super(plugin);
    }
    
    @Override
    public void onExecute(CommandSender sender, String[] args) {
        if (args.length < 2) {
            showUsage(sender);
            return;
        }
        
        final String action = args[0].toLowerCase();
        final String categoryName = args[1];
        
        switch (action) {
            case "create":
                createCategory(sender, categoryName);
                break;
            case "delete":
                deleteCategory(sender, categoryName);
                break;
            case "add":
                if (args.length < 3) {
                    sender.sendMessage("§cUsage: /warpcfg category add <category> <warp>");
                    return;
                }
                addWarpToCategory(sender, categoryName, args[2]);
                break;
            case "remove":
                if (args.length < 3) {
                    sender.sendMessage("§cUsage: /warpcfg category remove <category> <warp>");
                    return;
                }
                removeWarpFromCategory(sender, categoryName, args[2]);
                break;
            case "list":
                listCategory(sender, categoryName);
                break;
            default:
                showUsage(sender);
        }
    }
    
    private void createCategory(CommandSender sender, String name) {
        final WarpCategory category = new WarpCategory(name);
        
        if (!this.plugin.getCategoryContainer().add(category)) {
            sender.sendMessage("§cCategory '" + name + "' already exists!");
            return;
        }
        
        CategoryStorage.save(category, this.plugin);
        sender.sendMessage("§aCreated category '" + name + "'");
    }
    
    private void deleteCategory(CommandSender sender, String name) {
        final WarpCategory category = this.plugin.getCategoryContainer().get(name);
        
        if (category == null) {
            sender.sendMessage("§cCategory '" + name + "' not found!");
            return;
        }
        
        this.plugin.getCategoryContainer().remove(name);
        CategoryStorage.delete(category, this.plugin);
        sender.sendMessage("§aDeleted category '" + name + "'");
    }
    
    private void addWarpToCategory(CommandSender sender, String categoryName, String warpName) {
        final WarpCategory category = this.plugin.getCategoryContainer().get(categoryName);
        
        if (category == null) {
            sender.sendMessage("§cCategory '" + categoryName + "' not found!");
            return;
        }
        
        if (this.plugin.getProvider().getWarp(warpName) == null) {
            sender.sendMessage("§cWarp '" + warpName + "' not found!");
            return;
        }
        
        if (!category.addWarp(warpName)) {
            sender.sendMessage("§cWarp '" + warpName + "' is already in category '" + categoryName + "'!");
            return;
        }
        
        CategoryStorage.save(category, this.plugin);
        sender.sendMessage("§aAdded warp '" + warpName + "' to category '" + categoryName + "'");
    }
    
    private void removeWarpFromCategory(CommandSender sender, String categoryName, String warpName) {
        final WarpCategory category = this.plugin.getCategoryContainer().get(categoryName);
        
        if (category == null) {
            sender.sendMessage("§cCategory '" + categoryName + "' not found!");
            return;
        }
        
        if (!category.removeWarp(warpName)) {
            sender.sendMessage("§cWarp '" + warpName + "' is not in category '" + categoryName + "'!");
            return;
        }
        
        CategoryStorage.save(category, this.plugin);
        sender.sendMessage("§aRemoved warp '" + warpName + "' from category '" + categoryName + "'");
    }
    
    private void listCategory(CommandSender sender, String categoryName) {
        final WarpCategory category = this.plugin.getCategoryContainer().get(categoryName);
        
        if (category == null) {
            sender.sendMessage("§cCategory '" + categoryName + "' not found!");
            return;
        }
        
        sender.sendMessage("§eWarps in category '" + categoryName + "': §7" + 
            String.join(", ", category.getWarps()));
    }
    
    private void showUsage(CommandSender sender) {
        sender.sendMessage("§eCategory commands:");
        sender.sendMessage("§7/warpcfg category create <name>");
        sender.sendMessage("§7/warpcfg category delete <name>");
        sender.sendMessage("§7/warpcfg category add <category> <warp>");
        sender.sendMessage("§7/warpcfg category remove <category> <warp>");
        sender.sendMessage("§7/warpcfg category list <name>");
    }
    
    @Override
    public List<String> onTab(CommandSender sender, String[] args) {
        switch (args.length) {
            case 1:
                return getArray(args[0], "create", "delete", "add", "remove", "list");
            case 2:
                return this.plugin.getCategoryContainer().getAll().stream()
                    .map(WarpCategory::getName)
                    .filter(name -> name.toLowerCase().startsWith(args[1].toLowerCase()))
                    .collect(Collectors.toList());
            case 3:
                if ("add".equals(args[0]) || "remove".equals(args[0])) {
                    return getWarps(args[2]);
                }
                break;
        }
        return Collections.emptyList();
    }
}