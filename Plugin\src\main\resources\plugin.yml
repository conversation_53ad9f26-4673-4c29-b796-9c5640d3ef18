website: "https://seibel.dev"
version: "${project.version}"
name: "EssentialsWarpGUI"
author: "<PERSON> (aka Marcel<PERSON>1199)"
main: "de.marcely.warpgui.WarpGUIPlugin"
description: "Opens a gorgeous GUI for warp selection"
api-version: 1.13
softdepend: [ Essentials, EssentialsX ]

commands:
  warpcfg:
    description: "Modify the look-and-feel of the warps"
    usage: "/warpcfg [args...]"
    aliases: [ warpconfig, warpscfg, warpsconfig ]
  warpcategory:
    description: "Open a category of warps"
    usage: "/warpcategory [category]"
    aliases: [ warpcat, wcategory ]

permissions:
  warpcfg.cfg:
    description: "Grants access to the \"/warpcfg\" command"
    default: op
  warpcfg.category:
    description: "Grants access to the \"/warpcategory\" command"
    default: true
