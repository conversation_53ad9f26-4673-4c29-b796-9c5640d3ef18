package de.marcely.warpgui.storage;

import de.marcely.warpgui.category.CategoryContainer;
import de.marcely.warpgui.category.WarpCategory;
import de.marcely.warpgui.util.AdaptedGson;
import org.bukkit.plugin.Plugin;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.logging.Level;

public class CategoryStorage {
    
    public static void loadAll(CategoryContainer container) {
        try {
            final File folder = getFolder(container.getPlugin());
            container.clear();
            
            if (!folder.exists()) {
                return;
            }
            
            for (File file : folder.listFiles()) {
                if (!file.getName().endsWith(".json"))
                    continue;
                    
                load(container, file);
            }
        } catch (Exception e) {
            container.getPlugin().getLogger().log(Level.SEVERE, "Failed to load categories", e);
        }
    }
    
    private static void load(CategoryContainer container, File file) {
        try (InputStreamReader reader = new InputStreamReader(Files.newInputStream(file.toPath()), StandardCharsets.UTF_8)) {
            final WarpCategory category = AdaptedGson.get().fromJson(reader, WarpCategory.class);
            container.add(category);
        } catch (Exception e) {
            container.getPlugin().getLogger().log(Level.SEVERE, "Failed to load category from " + file.getName(), e);
        }
    }
    
    public static boolean save(WarpCategory category, Plugin plugin) {
        try {
            final File file = getFile(category, plugin);
            file.getParentFile().mkdirs();
            
            try (OutputStreamWriter writer = new OutputStreamWriter(Files.newOutputStream(file.toPath()), StandardCharsets.UTF_8)) {
                AdaptedGson.get().toJson(category, writer);
            }
            
            return true;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save category " + category.getName(), e);
            return false;
        }
    }
    
    public static boolean delete(WarpCategory category, Plugin plugin) {
        return getFile(category, plugin).delete();
    }
    
    private static File getFolder(Plugin plugin) {
        return new File(plugin.getDataFolder(), "categories");
    }
    
    private static File getFile(WarpCategory category, Plugin plugin) {
        return new File(getFolder(plugin), category.getName().toLowerCase() + ".json");
    }
}