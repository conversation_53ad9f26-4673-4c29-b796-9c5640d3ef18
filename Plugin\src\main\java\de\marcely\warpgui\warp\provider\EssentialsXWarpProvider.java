package de.marcely.warpgui.warp.provider;

import com.earth2me.essentials.Essentials;
import com.earth2me.essentials.IConf;
import de.marcely.warpgui.WarpGUIPlugin;
import de.marcely.warpgui.warp.EssentialsXWarp;
import lombok.Getter;
import net.ess3.api.IEssentials;
import org.bukkit.Bukkit;
import org.bukkit.command.PluginCommand;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class EssentialsXWarpProvider implements WarpProvider<EssentialsXWarp> {

    @Getter
    protected final WarpGUIPlugin plugin;
    @Getter
    protected final IEssentials hook;

    protected final Map<String, EssentialsXWarp> warps = new ConcurrentHashMap<>();

    @Getter
    private PluginCommand warpCommand;
    private IConf reloadListener;
    private int refreshTaskId = -1;
    private long lastWarpCount = 0;

    public EssentialsXWarpProvider(WarpGUIPlugin guiPlugin, Plugin essentialsPlugin) {
        this.plugin = guiPlugin;
        this.hook = (IEssentials) essentialsPlugin;
    }

    @Override
    public String getName() {
        return "EssentialsX";
    }

    @Override
    public String getVersion() {
        return this.hook.getDescription().getVersion();
    }

    @Override
    public void register() throws Exception {
        if ((this.warpCommand = ((JavaPlugin) this.hook).getCommand("warp")) == null)
            throw new IllegalStateException("EssentialsX didn't register warp command");

        this.hook.addReloadListener(this.reloadListener = () -> {
            // a tick later to make sure that everything has been loaded
            Bukkit.getScheduler().runTaskLater(
                    this.plugin,
                    this::fetchWarps,
                    1
            );
        });

        // Start periodic refresh task to detect new warps
        // Check every 30 seconds (600 ticks) for new warps
        this.refreshTaskId = Bukkit.getScheduler().runTaskTimerAsynchronously(
                this.plugin,
                this::checkForWarpChanges,
                600L, // Initial delay: 30 seconds
                600L  // Period: 30 seconds
        ).getTaskId();

        fetchWarps();
    }

    @Override
    public void unregister() {
        // Cancel the periodic refresh task
        if (this.refreshTaskId != -1) {
            Bukkit.getScheduler().cancelTask(this.refreshTaskId);
            this.refreshTaskId = -1;
        }

        try{
            // try to remove reload listener. yes, it's actually that complicated.
            final Field field = Essentials.class.getDeclaredField("confList");

            field.setAccessible(true);
            @SuppressWarnings("unchecked")
            Collection<IConf> confList = (Collection<IConf>) field.get(this.hook);
            confList.remove(this.reloadListener);
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public Collection<EssentialsXWarp> getWarps() {
        return this.warps.values();
    }

    @Override
    public @Nullable EssentialsXWarp getWarp(String name) {
        return this.warps.get(name.toLowerCase(Locale.ENGLISH));
    }

    @Override
    public void fetchWarps() {
        this.warps.clear();

        @SuppressWarnings("deprecation")
        Collection<String> warpNames = this.hook.getWarps().getList();
        for (String name : warpNames) {
            this.warps.put(
                    name.toLowerCase(Locale.ENGLISH),
                    new EssentialsXWarp(this, name));
        }

        this.lastWarpCount = warpNames.size();
        updateGUIHooks();
    }

    /**
     * Checks if the number of warps has changed and refreshes if needed.
     * This method is called periodically to detect new warps without requiring a reload.
     */
    private void checkForWarpChanges() {
        try {
            @SuppressWarnings("deprecation")
            Collection<String> currentWarps = this.hook.getWarps().getList();
            long currentCount = currentWarps.size();

            // If the warp count changed, refresh the warps
            if (currentCount != this.lastWarpCount) {
                this.plugin.getLogger().info("Detected warp changes (" + this.lastWarpCount + " -> " + currentCount + "), refreshing warps...");

                // Schedule the refresh on the main thread
                Bukkit.getScheduler().runTask(this.plugin, this::fetchWarps);
            }
        } catch (Exception e) {
            this.plugin.getLogger().warning("Failed to check for warp changes: " + e.getMessage());
        }
    }
}
