package de.marcely.warpgui.category;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

@Data
public class WarpCategory {
    private final String name;
    private String displayName;
    private List<String> warps = new ArrayList<>();
    
    public WarpCategory(String name) {
        this.name = name;
        this.displayName = name;
    }
    
    public boolean addWarp(String warpName) {
        if (!warps.contains(warpName.toLowerCase())) {
            warps.add(warpName.toLowerCase());
            return true;
        }
        return false;
    }
    
    public boolean removeWarp(String warpName) {
        return warps.remove(warpName.toLowerCase());
    }
    
    public boolean hasWarp(String warpName) {
        return warps.contains(warpName.toLowerCase());
    }
}