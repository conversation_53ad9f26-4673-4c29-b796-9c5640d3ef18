package de.marcely.warpgui.command;

import de.marcely.warpgui.WarpGUIPlugin;
import de.marcely.warpgui.category.WarpCategory;
import de.marcely.warpgui.GUIWarp;
import de.marcely.warpgui.Message;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class WarpCategoryCommand implements CommandExecutor, TabCompleter {
    
    private final WarpGUIPlugin plugin;
    
    public WarpCategoryCommand(WarpGUIPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cThis command can only be used by players!");
            return true;
        }
        
        final Player player = (Player) sender;
        
        if (!player.hasPermission("warpcfg.category")) {
            Message.INSUFFICIENT_PERMISSIONS.send(sender);
            return true;
        }
        
        if (args.length == 0) {
            // Show all categories
            final List<String> categories = plugin.getCategoryContainer().getAll().stream()
                .map(WarpCategory::getName)
                .collect(Collectors.toList());
                
            if (categories.isEmpty()) {
                player.sendMessage("§cNo categories found!");
            } else {
                player.sendMessage("§eAvailable categories: §7" + String.join(", ", categories));
            }
            return true;
        }
        
        final String categoryName = args[0];
        final WarpCategory category = plugin.getCategoryContainer().get(categoryName);
        
        if (category == null) {
            player.sendMessage("§cCategory '" + categoryName + "' not found!");
            return true;
        }
        
        // Get warps in this category that exist and player has permission for
        final List<GUIWarp> categoryWarps = category.getWarps().stream()
            .map(warpName -> plugin.getContainer().getHooked(warpName))
            .filter(warp -> warp != null && warp.hasHook() && warp.getHook().hasPermission(player))
            .collect(Collectors.toList());
            
        if (categoryWarps.isEmpty()) {
            player.sendMessage("§cNo accessible warps found in category '" + categoryName + "'!");
            return true;
        }
        
        // Open category GUI
        plugin.getRenderer().openCategory(player, category, categoryWarps);
        return true;
    }
    
    @Nullable
    @Override
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (args.length == 1) {
            return plugin.getCategoryContainer().getAll().stream()
                .map(WarpCategory::getName)
                .filter(name -> name.toLowerCase().startsWith(args[0].toLowerCase()))
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}