package de.marcely.warpgui.category;

import de.marcely.warpgui.WarpGUIPlugin;
import lombok.Getter;
import java.util.Collection;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CategoryContainer {
    @Getter
    private final WarpGUIPlugin plugin;
    private final Map<String, WarpCategory> categories = new ConcurrentHashMap<>();
    
    public CategoryContainer(WarpGUIPlugin plugin) {
        this.plugin = plugin;
    }
    
    public Collection<WarpCategory> getAll() {
        return categories.values();
    }
    
    public WarpCategory get(String name) {
        return categories.get(name.toLowerCase(Locale.ENGLISH));
    }
    
    public boolean add(WarpCategory category) {
        return categories.putIfAbsent(category.getName().toLowerCase(Locale.ENGLISH), category) == null;
    }
    
    public boolean remove(String name) {
        return categories.remove(name.toLowerCase(Locale.ENGLISH)) != null;
    }
    
    public void clear() {
        categories.clear();
    }
}