package de.marcely.warpgui.command.config;

import de.marcely.warpgui.WarpGUIPlugin;
import de.marcely.warpgui.Message;
import de.marcely.warpgui.command.Command;
import org.bukkit.command.CommandSender;

import java.util.Collections;
import java.util.List;

public class RefreshCommand extends Command.Executor {

    public RefreshCommand(WarpGUIPlugin plugin) {
        super(plugin);
    }

    @Override
    public void onExecute(CommandSender sender, String[] args) {
        sender.sendMessage("§eRefreshing warps from " + this.plugin.getProvider().getName() + "...");
        
        // Fetch warps from the provider
        this.plugin.getProvider().fetchWarps();
        
        final int warpCount = this.plugin.getProvider().getWarps().size();
        sender.sendMessage("§aSuccessfully refreshed! Found " + warpCount + " warp(s).");
    }

    @Override
    public List<String> onTab(CommandSender sender, String[] args) {
        return Collections.emptyList();
    }
}
