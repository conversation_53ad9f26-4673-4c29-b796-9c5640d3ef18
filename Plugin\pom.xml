<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>de.marcely.warpgui</groupId>
    <artifactId>Plugin</artifactId>
    <version>3.1</version>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <parent>
        <groupId>de.marcely.warpgui</groupId>
        <artifactId>Parent</artifactId>
        <version>3.1</version>
    </parent>

    <repositories>
        <repository>
            <id>essentialsx-repo</id>
            <url>https://repo.essentialsx.net/releases/</url>
        </repository>
        <repository>
            <id>papermc-repo</id>
            <url>https://repo.papermc.io/repository/maven-public/</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>de.marcely.warpgui</groupId>
            <artifactId>Legacy</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>24.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.32</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.cryptomorin</groupId>
            <artifactId>XSeries</artifactId>
            <version>10.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>1.20.6-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>net.essentialsx</groupId>
            <artifactId>EssentialsX</artifactId>
            <version>2.20.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.bstats</groupId>
            <artifactId>bstats-bukkit</artifactId>
            <version>3.0.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.2</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                    <relocations>
                        <relocation>
                            <pattern>com.google.gson</pattern>
                            <shadedPattern>de.marcely.warpgui.libraries.com.google.gson</shadedPattern>
                        </relocation>
                        <relocation>
                            <pattern>com.cryptomorin.xseries</pattern>
                            <shadedPattern>de.marcely.warpgui.libraries.com.cryptomorin.xseries
                            </shadedPattern>
                        </relocation>
                        <relocation>
                            <pattern>org.bstats</pattern>
                            <shadedPattern>de.marcely.warpgui.libraries.org.bstats
                            </shadedPattern>
                        </relocation>
                    </relocations>
                    <artifactSet>
                        <excludes>
                            <exclude>junit:junit</exclude>
                            <exclude>org.hamcrest:*</exclude>
                        </excludes>
                    </artifactSet>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <finalName>${project.name}</finalName>
                    <outputDirectory>${project.basedir}/out/</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.3.2</version>
                <executions>
                    <execution>
                        <id>auto-clean</id>
                        <phase>install</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/plugin.yml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>**/plugin.yml</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>debug</id>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.2.1</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <configuration>
                                    <executable>cmd</executable>
                                    <workingDirectory></workingDirectory>
                                    <arguments>
                                        <argument>/C</argument>
                                        <argument>"${project.basedir}/copy.cmd"</argument>
                                    </arguments>
                                </configuration>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
